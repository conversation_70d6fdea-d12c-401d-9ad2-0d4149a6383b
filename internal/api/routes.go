package api

import (
	"net/http"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	"real-time-ca-service/internal/config"
	"real-time-ca-service/internal/metrics"
	"real-time-ca-service/internal/services"
)

// NewRouter creates a new HTTP router
func NewRouter(twitterService *services.TwitterService, caService *services.CAService, tokenService *services.TokenService, cfg *config.Config) http.Handler {
	// Create handler
	handler := NewHandler(twitterService, caService, tokenService)

	// Create router with default middleware (Logger, Recovery)
	if cfg.Server.GinMode == "release" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}
	router := gin.Default()

	// Add custom middleware
	router.Use(corsMiddleware())
	router.Use(metricsMiddleware())
	router.Use(customLoggerMiddleware())

	router.GET("/api-docs/swagger.json", func(c *gin.Context) {
		if _, err := os.Stat("docs/swagger.json"); os.IsNotExist(err) {
			c.File("../../docs/swagger.json")
			return
		} else if err != nil {
			c.AbortWithStatusJSON(500, gin.H{"error": "Internal server error"})
			return
		}
		c.File("docs/swagger.json")
	})

	// Swagger documentation route
	// Uncomment when Swagger dependencies are added
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler, ginSwagger.URL("/api-docs/swagger.json")))

	// Set up metrics endpoint if enabled
	if cfg.Metrics.Enabled {
		router.GET(cfg.Metrics.Path, gin.WrapH(promhttp.Handler()))
		log.Info().Str("path", cfg.Metrics.Path).Msg("Metrics endpoint enabled")
	}

	// API routes group
	api := router.Group("/api")
	{
		// Register routes
		api.GET("/health", handler.HealthCheck)
		api.GET("/tweets", handler.GetTweets)
		api.GET("/tweet/:tweet_id", handler.GetTweetByID)
		api.GET("/recognized-cas", handler.GetRecognizedCAs)
		api.GET("/recognized-ca/:address/:chain_id", handler.GetRecognizedCAByAddressAndChainID)
		// soicaldata webhook
		api.POST("/webhook/twitter", handler.HandleWebhook)
		api.GET("/list-tweets", handler.GetListTweets)
		api.GET("/announcement-statistics", handler.GetAnnouncementStatistics)
		api.GET("/telegram-notification-stats", handler.GetTelegramNotificationStats)
		// api.DELETE("/recognized-cas/:address", handler.DeleteRecognizedCA)
		// api.POST("/recognized-cas", handler.AddRecognizedCA)
	}

	return router
}

// customLoggerMiddleware logs all requests
func customLoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Start timer
		start := time.Now()

		// Process request
		c.Next()

		// Skip logging for health checks to reduce noise
		if c.Request.URL.Path != "/api/health" {
			// Calculate latency
			duration := time.Since(start)

			// Log request details
			log.Info().
				Str("method", c.Request.Method).
				Str("path", c.Request.URL.Path).
				Int("status", c.Writer.Status()).
				Dur("duration", duration).
				Str("ip", c.ClientIP()).
				Str("user_agent", c.Request.UserAgent()).
				Msg("Request processed")
		}
	}
}

// metricsMiddleware collects metrics for all requests
func metricsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Start timer
		start := time.Now()

		// Process request
		c.Next()

		// Skip metrics for health checks to reduce noise
		if c.Request.URL.Path != "/api/health" {
			// Calculate latency
			duration := time.Since(start)

			// Record metrics
			metrics.APIRequestsTotal.WithLabelValues(c.Request.URL.Path, c.Request.Method, http.StatusText(c.Writer.Status())).Inc()
			metrics.APIRequestDuration.WithLabelValues(c.Request.URL.Path, c.Request.Method).Observe(duration.Seconds())
		}
	}
}

// corsMiddleware adds CORS headers
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Add CORS headers
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		// Handle preflight requests
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusOK)
			return
		}

		// Process request
		c.Next()
	}
}
